#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期刊匹配工具
用于检索C等级以上（包含C）的与"天线"、"通信"相关的期刊
"""

import json
from typing import List, Dict, Any


class JournalMatcher:
    """期刊匹配器类"""

    def __init__(self, data_file: str = "四川大学期刊数据_20250708_173023.json"):
        """
        初始化期刊匹配器

        Args:
            data_file: 期刊数据文件路径
        """
        self.data_file = data_file
        self.journals = []
        self.load_data()

    def load_data(self):
        """加载期刊数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                self.journals = json.load(f)
            print(f"成功加载 {len(self.journals)} 条期刊数据")
        except FileNotFoundError:
            print(f"错误：找不到数据文件 {self.data_file}")
        except json.JSONDecodeError:
            print(f"错误：数据文件 {self.data_file} 格式不正确")
        except Exception as e:
            print(f"加载数据时发生错误：{e}")

    def is_grade_c_or_above(self, grade: str) -> bool:
        """
        判断期刊分级是否为C等级以上（包含C）

        Args:
            grade: 期刊分级（如 A, A-, B, B+, C, D 等）

        Returns:
            bool: 是否为C等级以上
        """
        if not grade:
            return False

        # 定义等级优先级（数字越小等级越高）
        grade_priority = {
            'A': 1,
            'A-': 2,
            'B+': 3,
            'B': 4,
            'B-': 5,
            'C+': 6,
            'C': 7,
            'C-': 8,
            'D': 9
        }

        # 获取当前分级的优先级
        current_priority = grade_priority.get(grade.strip(), 999)

        # C等级的优先级是7，所以小于等于7的都符合条件
        return current_priority <= 7

    def is_antenna_or_communication_related(self, journal: Dict[str, Any]) -> bool:
        """
        判断期刊是否与天线、通信相关

        Args:
            journal: 期刊信息字典

        Returns:
            bool: 是否相关
        """
        # 定义关键词（中英文）
        keywords = [
            # 中文关键词
            '天线', '通信', '通讯', '电信', '无线', '射频', '微波', '信号', '传输', '电磁',

            # 英文关键词
            'antenna', 'antennas', 'communication', 'communications', 'wireless',
            'radio', 'microwave', 'signal', 'transmission', 'electromagnetic',
            'rf', 'commun', 'wirel', 'antenn'
        ]

        # 检查期刊全称和简称
        full_name = journal.get('期刊全称', '').lower()
        short_name = journal.get('期刊简称', '').lower()
        subject = journal.get('学科大类', '').lower()

        # 在期刊名称和学科大类中搜索关键词
        search_text = f"{full_name} {short_name} {subject}"

        for keyword in keywords:
            if keyword.lower() in search_text:
                return True

        return False

    def search_journals(self, keywords: List[str] = None) -> List[Dict[str, Any]]:
        """
        搜索符合条件的期刊

        Args:
            keywords: 额外的关键词列表
            min_grade: 最低等级要求

        Returns:
            List[Dict]: 符合条件的期刊列表
        """
        results = []

        for journal in self.journals:
            # 检查分级
            if not self.is_grade_c_or_above(journal.get('分级', '')):
                continue

            # 检查是否与天线、通信相关
            if not self.is_antenna_or_communication_related(journal):
                continue

            # 如果提供了额外关键词，进行额外检查
            if keywords:
                journal_text = f"{journal.get('期刊全称', '')} {journal.get('期刊简称', '')} {journal.get('学科大类', '')}".lower()
                if not any(keyword.lower() in journal_text for keyword in keywords):
                    continue

            results.append(journal)

        return results

    def print_results(self, results: List[Dict[str, Any]]):
        """
        打印搜索结果

        Args:
            results: 期刊列表
        """
        if not results:
            print("未找到符合条件的期刊")
            return

        print(f"\n找到 {len(results)} 个符合条件的期刊：")
        print("=" * 100)

        # 按分级排序
        grade_order = {'A': 1, 'A-': 2, 'B+': 3, 'B': 4, 'B-': 5, 'C+': 6, 'C': 7, 'C-': 8}
        results.sort(key=lambda x: grade_order.get(x.get('分级', ''), 999))

        for i, journal in enumerate(results, 1):
            print(f"{i:3d}. 【{journal.get('分级', 'N/A')}级】{journal.get('期刊全称', 'N/A')}")
            print(f"     期刊简称: {journal.get('期刊简称', 'N/A')}")
            print(f"     ISSN: {journal.get('ISSN', 'N/A')}")
            print(f"     学科大类: {journal.get('学科大类', 'N/A')}")
            if journal.get('预警信息'):
                print(f"     预警信息: {journal.get('预警信息')}")
            print("-" * 100)

    def export_results(self, results: List[Dict[str, Any]], filename: str = "匹配结果.json"):
        """
        导出搜索结果到文件

        Args:
            results: 期刊列表
            filename: 输出文件名
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"结果已导出到 {filename}")
        except Exception as e:
            print(f"导出文件时发生错误：{e}")


def main():
    """主函数"""
    print("期刊匹配工具 - 检索C等级以上的天线、通信相关期刊")
    print("=" * 60)

    # 创建匹配器实例
    matcher = JournalMatcher()

    if not matcher.journals:
        print("无法加载期刊数据，程序退出")
        return

    # 搜索符合条件的期刊
    print("正在搜索C等级以上的天线、通信相关期刊...")
    results = matcher.search_journals()

    # 打印结果
    matcher.print_results(results)

    # 询问是否导出结果
    if results:
        export_choice = input("\n是否导出结果到文件？(y/n): ").strip().lower()
        if export_choice in ['y', 'yes', '是']:
            filename = input("请输入文件名（默认：天线通信期刊匹配结果.json）: ").strip()
            if not filename:
                filename = "天线通信期刊匹配结果.json"
            matcher.export_results(results, filename)


if __name__ == "__main__":
    main()