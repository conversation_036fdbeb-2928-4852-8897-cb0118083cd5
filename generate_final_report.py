#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成最终的期刊影响因子分区报告
"""

import json
import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side


def create_final_report():
    """创建最终的期刊报告"""
    
    # 读取数据
    with open('期刊影响因子分区信息.json', 'r', encoding='utf-8') as f:
        journals = json.load(f)
    
    print(f"加载了 {len(journals)} 条期刊数据")
    
    # 创建DataFrame
    df = pd.DataFrame(journals)
    
    # 重新排列列的顺序
    columns_order = [
        '序号', '分级', '期刊全称', '期刊简称', 'ISSN', 
        '影响因子', '中科院分区', 'JCR分区', '学科大类', '数据来源'
    ]
    df = df.reindex(columns=columns_order)
    
    # 按分级排序
    grade_order = {'A': 1, 'A-': 2, 'B+': 3, 'B': 4, 'B-': 5, 'C+': 6, 'C': 7, 'C-': 8}
    df['分级排序'] = df['分级'].map(grade_order)
    df = df.sort_values(['分级排序', '影响因子'], ascending=[True, False]).drop('分级排序', axis=1)
    
    # 重置索引
    df.reset_index(drop=True, inplace=True)
    df.index = df.index + 1
    
    # 导出到Excel
    filename = "天线通信期刊影响因子分区完整报告.xlsx"
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='期刊影响因子分区', index_label='排名')
        
        # 获取工作表对象
        worksheet = writer.sheets['期刊影响因子分区']
        
        # 设置列宽
        column_widths = {
            'A': 8,   # 排名
            'B': 8,   # 序号
            'C': 8,   # 分级
            'D': 55,  # 期刊全称
            'E': 25,  # 期刊简称
            'F': 15,  # ISSN
            'G': 12,  # 影响因子
            'H': 12,  # 中科院分区
            'I': 12,  # JCR分区
            'J': 15,  # 学科大类
            'K': 15   # 数据来源
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
    
    print(f"Excel报告已生成：{filename}")
    
    # 统计信息
    print("\n=== 统计信息 ===")
    print(f"总期刊数量：{len(df)}")
    
    # 按分级统计
    grade_stats = df['分级'].value_counts().sort_index()
    print("\n按分级统计：")
    for grade, count in grade_stats.items():
        print(f"  {grade}级：{count}个")
    
    # 按影响因子统计
    if_data = pd.to_numeric(df[df['影响因子'] != '']['影响因子'], errors='coerce').dropna()
    if len(if_data) > 0:
        print(f"\n影响因子统计（{len(if_data)}个期刊有数据）：")
        print(f"  最高影响因子：{if_data.max():.3f}")
        print(f"  最低影响因子：{if_data.min():.3f}")
        print(f"  平均影响因子：{if_data.mean():.3f}")
    
    # 按分区统计
    sci_zone_stats = df[df['中科院分区'] != '']['中科院分区'].value_counts()
    if len(sci_zone_stats) > 0:
        print(f"\n中科院分区统计（{len(sci_zone_stats)}个期刊有数据）：")
        for zone, count in sci_zone_stats.items():
            print(f"  {zone}：{count}个")
    
    jcr_zone_stats = df[df['JCR分区'] != '']['JCR分区'].value_counts()
    if len(jcr_zone_stats) > 0:
        print(f"\nJCR分区统计（{len(jcr_zone_stats)}个期刊有数据）：")
        for zone, count in jcr_zone_stats.items():
            print(f"  {zone}：{count}个")
    
    # 显示顶级期刊
    print("\n=== 顶级期刊推荐（影响因子>10） ===")
    # 筛选有影响因子数据的期刊
    df_with_if = df[df['影响因子'] != ''].copy()
    if len(df_with_if) > 0:
        df_with_if['影响因子_数值'] = pd.to_numeric(df_with_if['影响因子'], errors='coerce')
        top_journals = df_with_if[df_with_if['影响因子_数值'] > 10]
    else:
        top_journals = pd.DataFrame()
    if len(top_journals) > 0:
        for _, journal in top_journals.iterrows():
            print(f"【{journal['分级']}级】{journal['期刊全称']}")
            print(f"  影响因子：{journal['影响因子']} | 中科院分区：{journal['中科院分区']} | JCR分区：{journal['JCR分区']}")
            print(f"  ISSN：{journal['ISSN']} | 学科：{journal['学科大类']}")
            print("-" * 80)
    else:
        print("暂无影响因子>10的期刊数据")
    
    return df


def print_summary_table():
    """打印汇总表格"""
    with open('期刊影响因子分区信息.json', 'r', encoding='utf-8') as f:
        journals = json.load(f)
    
    # 筛选有影响因子数据的期刊
    journals_with_if = [j for j in journals if j.get('影响因子', '') != '']
    
    print("\n=== 天线通信相关期刊影响因子汇总表 ===")
    print("=" * 120)
    print(f"{'排名':<4} {'分级':<6} {'期刊全称':<50} {'影响因子':<10} {'中科院分区':<10} {'JCR分区':<8}")
    print("=" * 120)
    
    # 按影响因子排序
    journals_with_if.sort(key=lambda x: float(x['影响因子']), reverse=True)
    
    for i, journal in enumerate(journals_with_if[:20], 1):  # 显示前20个
        name = journal['期刊全称']
        if len(name) > 48:
            name = name[:45] + "..."
        
        print(f"{i:<4} {journal['分级']:<6} {name:<50} {journal['影响因子']:<10} {journal['中科院分区']:<10} {journal['JCR分区']:<8}")
    
    print("=" * 120)
    print(f"注：共找到 {len(journals_with_if)} 个期刊有影响因子数据，以上显示前20个")


if __name__ == "__main__":
    print("天线通信期刊影响因子分区报告生成器")
    print("=" * 50)
    
    # 生成完整报告
    df = create_final_report()
    
    # 打印汇总表格
    print_summary_table()
    
    print("\n报告生成完成！")
