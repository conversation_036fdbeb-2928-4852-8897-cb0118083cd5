#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四川大学期刊爬虫 - 简化版本
直接访问数据接口，无需登录
"""

import requests
import pandas as pd
import json
from datetime import datetime

def get_journal_data():
    """获取期刊数据"""
    print("=" * 60)
    print("四川大学期刊数据获取工具")
    print("=" * 60)
    
    # 数据接口URL
    data_url = "https://ir.scu.edu.cn/style/d2.json"
    
    try:
        print(f"正在访问数据接口: {data_url}")
        
        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8'
        }
        
        # 发送请求
        response = requests.get(data_url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            print("✓ 成功访问数据接口")
            
            # 解析JSON数据
            data = response.json()
            print(f"✓ 获取到 {len(data)} 条期刊数据")
            
            return data
        else:
            print(f"✗ 请求失败，状态码: {response.status_code}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ 网络请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"✗ JSON解析失败: {e}")
        return None
    except Exception as e:
        print(f"✗ 获取数据失败: {e}")
        return None

def process_data(raw_data):
    """处理数据"""
    if not raw_data:
        return []
    
    print("正在处理数据...")
    
    processed_data = []
    for i, item in enumerate(raw_data):
        try:
            journal_info = {
                '序号': i + 1,
                '期刊全称': item.get('期刊全称', '').strip(),
                '期刊简称': item.get('期刊简称', '').strip(),
                'ISSN': item.get('issn', '').strip(),
                '学科大类': item.get('学科大类', '').strip(),
                '分级': item.get('分级', '').strip(),
                '预警信息': item.get('warningMsg', '').strip()
            }
            
            # 只保存有期刊全称的记录
            if journal_info['期刊全称']:
                processed_data.append(journal_info)
        
        except Exception as e:
            print(f"处理第{i+1}条数据时出错: {e}")
            continue
    
    print(f"✓ 成功处理 {len(processed_data)} 条有效数据")
    return processed_data

def save_data(data):
    """保存数据到文件"""
    if not data:
        print("✗ 没有数据可保存")
        return False
    
    try:
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存为Excel
        excel_filename = f'四川大学期刊数据_{timestamp}.xlsx'
        df.to_excel(excel_filename, index=False, engine='openpyxl')
        print(f"✓ Excel文件已保存: {excel_filename}")
        
        # 保存为CSV
        csv_filename = f'四川大学期刊数据_{timestamp}.csv'
        df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
        print(f"✓ CSV文件已保存: {csv_filename}")
        
        # 保存为JSON
        json_filename = f'四川大学期刊数据_{timestamp}.json'
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"✓ JSON文件已保存: {json_filename}")
        
        return True
        
    except Exception as e:
        print(f"✗ 保存数据失败: {e}")
        return False

def show_statistics(data):
    """显示数据统计"""
    if not data:
        return
    
    print("\n" + "=" * 60)
    print("📊 数据统计")
    print("=" * 60)
    
    print(f"总计: {len(data)} 条期刊数据")
    
    # 按分级统计
    grade_count = {}
    for item in data:
        grade = item.get('分级', 'Unknown')
        grade_count[grade] = grade_count.get(grade, 0) + 1
    
    print("\n分级分布:")
    for grade, count in sorted(grade_count.items()):
        percentage = (count / len(data)) * 100
        print(f"  {grade}: {count} 条 ({percentage:.1f}%)")
    
    # 按学科大类统计
    subject_count = {}
    for item in data:
        subject = item.get('学科大类', 'Unknown')
        subject_count[subject] = subject_count.get(subject, 0) + 1
    
    print("\n学科大类分布（前10名）:")
    sorted_subjects = sorted(subject_count.items(), key=lambda x: x[1], reverse=True)
    for subject, count in sorted_subjects[:10]:
        percentage = (count / len(data)) * 100
        print(f"  {subject}: {count} 条 ({percentage:.1f}%)")
    
    # 预警期刊统计
    warning_count = sum(1 for item in data if item.get('预警信息', '').strip())
    if warning_count > 0:
        print(f"\n⚠️ 预警期刊: {warning_count} 条")
    
    # 显示前5条数据作为示例
    print("\n📋 数据示例（前5条）:")
    for i, item in enumerate(data[:5]):
        print(f"  {i+1}. {item['期刊全称']} ({item['分级']}) - {item['学科大类']}")

def main():
    """主函数"""
    try:
        # 1. 获取数据
        raw_data = get_journal_data()
        if not raw_data:
            print("❌ 获取数据失败")
            return
        
        # 2. 处理数据
        processed_data = process_data(raw_data)
        if not processed_data:
            print("❌ 处理数据失败")
            return
        
        # 3. 显示统计
        show_statistics(processed_data)
        
        # 4. 保存数据
        if save_data(processed_data):
            print("\n🎉 数据获取和保存完成！")
            print("\n📁 生成的文件:")
            print("  - Excel格式：四川大学期刊数据_[时间戳].xlsx")
            print("  - CSV格式：四川大学期刊数据_[时间戳].csv")
            print("  - JSON格式：四川大学期刊数据_[时间戳].json")
        else:
            print("❌ 保存数据失败")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
