#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四川大学期刊爬虫 - 最终解决方案
基于页面分析结果的优化版本
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import pandas as pd
import time
import json

class SCUJournalCrawlerFinal:
    def __init__(self):
        self.driver = None
        self.journals_data = []
    
    def setup_driver(self):
        """设置Chrome浏览器"""
        try:
            options = webdriver.ChromeOptions()
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✓ Chrome浏览器启动成功")
            return True
        except Exception as e:
            print(f"✗ 浏览器启动失败: {e}")
            return False
    
    def login(self, username, password):
        """登录系统"""
        try:
            login_url = "https://ir.scu.edu.cn/publicUser/toLogin"
            print(f"访问登录页面: {login_url}")
            self.driver.get(login_url)
            
            # 等待登录表单加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "userName"))
            )
            
            # 填写用户名和密码
            self.driver.find_element(By.ID, "userName").send_keys(username)
            self.driver.find_element(By.ID, "password").send_keys(password)
            
            print("✓ 用户名和密码已填写")
            print("请在浏览器中输入验证码，然后在控制台按回车继续...")
            input("按回车键继续...")
            
            # 点击登录按钮
            self.driver.find_element(By.ID, "loginbtn1").click()
            
            # 等待登录完成
            time.sleep(3)
            
            # 检查是否登录成功
            current_url = self.driver.current_url
            if "toLogin" not in current_url:
                print("✓ 登录成功")
                return True
            else:
                print("✗ 登录失败，请检查用户名、密码或验证码")
                return False
                
        except Exception as e:
            print(f"✗ 登录过程出错: {e}")
            return False
    
    def wait_for_data_load(self):
        """等待数据加载完成"""
        try:
            print("等待JavaScript数据加载...")
            
            # 方法1：等待json变量存在且有数据
            try:
                WebDriverWait(self.driver, 30).until(
                    lambda driver: driver.execute_script(
                        "return typeof json !== 'undefined' && json && json.length > 0"
                    )
                )
                print("✓ JavaScript数据加载完成")
                return True
            except TimeoutException:
                print("⚠ 等待JavaScript数据超时，尝试手动触发...")
                
                # 方法2：手动触发load()函数
                try:
                    self.driver.execute_script("if (typeof load === 'function') load();")
                    time.sleep(5)
                    
                    # 再次检查数据
                    has_data = self.driver.execute_script(
                        "return typeof json !== 'undefined' && json && json.length > 0"
                    )
                    if has_data:
                        print("✓ 手动触发数据加载成功")
                        return True
                    else:
                        print("⚠ 手动触发后仍无数据")
                        
                except Exception as e:
                    print(f"⚠ 手动触发失败: {e}")
                
                # 方法3：等待页面完全加载后再试
                print("等待页面完全加载...")
                time.sleep(10)
                
                has_data = self.driver.execute_script(
                    "return typeof json !== 'undefined' && json && json.length > 0"
                )
                if has_data:
                    print("✓ 延时等待后数据加载成功")
                    return True
                
                return False
                
        except Exception as e:
            print(f"✗ 等待数据加载失败: {e}")
            return False
    
    def get_journal_data(self):
        """获取期刊数据"""
        try:
            print("从JavaScript获取期刊数据...")
            
            # 获取JSON数据
            json_data = self.driver.execute_script("return json;")
            
            if not json_data:
                print("✗ 未获取到数据")
                return []
            
            print(f"✓ 获取到 {len(json_data)} 条原始数据")
            
            # 处理数据
            journals = []
            for i, item in enumerate(json_data):
                try:
                    journal_info = {
                        '序号': str(i + 1),
                        '期刊全称': item.get('期刊全称', '').strip(),
                        '期刊简称': item.get('期刊简称', '').strip(),
                        'ISSN': item.get('issn', '').strip(),
                        '学科大类': item.get('学科大类', '').strip(),
                        '分级': item.get('分级', '').strip()
                    }
                    
                    # 只保存有期刊全称的记录
                    if journal_info.get('期刊全称'):
                        journals.append(journal_info)
                        
                        # 显示前5条数据用于验证
                        if i < 5:
                            print(f"  第{i+1}条: {journal_info['期刊全称']} - {journal_info['分级']}")
                
                except Exception as e:
                    print(f"处理第{i+1}条数据时出错: {e}")
                    continue
            
            print(f"✓ 成功处理 {len(journals)} 条期刊数据")
            return journals
            
        except Exception as e:
            print(f"✗ 获取期刊数据失败: {e}")
            return []
    
    def save_data(self, journals):
        """保存数据到文件"""
        if not journals:
            print("✗ 没有数据可保存")
            return False
        
        try:
            # 创建DataFrame
            df = pd.DataFrame(journals)
            
            # 保存为Excel
            excel_filename = '四川大学期刊数据_最终版.xlsx'
            df.to_excel(excel_filename, index=False, engine='openpyxl')
            print(f"✓ 数据已保存到 {excel_filename}")
            
            # 保存为CSV
            csv_filename = '四川大学期刊数据_最终版.csv'
            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            print(f"✓ 数据已保存到 {csv_filename}")
            
            # 保存为JSON（备份）
            json_filename = '四川大学期刊数据_最终版.json'
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(journals, f, ensure_ascii=False, indent=2)
            print(f"✓ 数据已保存到 {json_filename}")
            
            # 显示统计信息
            print(f"\n📊 数据统计:")
            print(f"总计: {len(journals)} 条期刊数据")
            
            # 按分级统计
            grade_count = {}
            for journal in journals:
                grade = journal.get('分级', 'Unknown')
                grade_count[grade] = grade_count.get(grade, 0) + 1
            
            print("分级分布:")
            for grade, count in sorted(grade_count.items()):
                print(f"  {grade}: {count} 条")
            
            return True
            
        except Exception as e:
            print(f"✗ 保存数据失败: {e}")
            return False
    
    def crawl(self, username, password):
        """主爬取流程"""
        try:
            print("=" * 60)
            print("四川大学期刊爬虫 - 最终版本")
            print("=" * 60)
            
            # 1. 设置浏览器
            if not self.setup_driver():
                return False
            
            # 2. 登录
            if not self.login(username, password):
                return False
            
            # 3. 等待数据加载
            if not self.wait_for_data_load():
                print("✗ 数据加载失败，但继续尝试...")
            
            # 4. 获取数据
            journals = self.get_journal_data()
            if not journals:
                print("✗ 未获取到期刊数据")
                return False
            
            # 5. 保存数据
            if not self.save_data(journals):
                return False
            
            print("\n🎉 爬取完成！")
            return True
            
        except Exception as e:
            print(f"✗ 爬取过程出错: {e}")
            return False
        finally:
            if self.driver:
                print("\n5秒后关闭浏览器...")
                time.sleep(5)
                self.driver.quit()

def main():
    crawler = SCUJournalCrawlerFinal()
    
    # 获取登录信息
    print("请输入登录信息:")
    username = input("用户名（一卡通号）: ").strip()
    password = input("密码: ").strip()
    
    if not username or not password:
        print("✗ 用户名和密码不能为空")
        return
    
    # 开始爬取
    success = crawler.crawl(username, password)
    
    if success:
        print("\n✅ 爬取成功完成！")
        print("生成的文件:")
        print("  - 四川大学期刊数据_最终版.xlsx")
        print("  - 四川大学期刊数据_最终版.csv") 
        print("  - 四川大学期刊数据_最终版.json")
    else:
        print("\n❌ 爬取失败")

if __name__ == "__main__":
    main()
